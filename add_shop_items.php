<?php
// Easy script to add new items to the shop
require_once "db.php";

echo "Adding New Shop Items\n";
echo "====================\n";

// Define new items to add
$newItems = [
    // Example items - modify these as needed
    [
        'item_id' => 186000001,
        'name' => 'Health Potion (x1)',
        'description' => 'Restores health instantly',
        'price' => 5,
        'quantity' => 1,
        'rarity' => 'Common'
    ],
    [
        'item_id' => 186000001,
        'name' => 'Health Potion (x10)',
        'description' => 'Restores health instantly - Pack of 10',
        'price' => 40,
        'quantity' => 10,
        'rarity' => 'Common'
    ],
    [
        'item_id' => 186000002,
        'name' => 'Mana Potion (x1)',
        'description' => 'Restores mana instantly',
        'price' => 5,
        'quantity' => 1,
        'rarity' => 'Common'
    ],
    [
        'item_id' => 186000003,
        'name' => 'Rare Weapon',
        'description' => 'A powerful rare weapon',
        'price' => 100,
        'quantity' => 1,
        'rarity' => 'Heroic'
    ],
    [
        'item_id' => 186000004,
        'name' => 'Legendary Armor',
        'description' => 'Epic armor piece',
        'price' => 250,
        'quantity' => 1,
        'rarity' => 'Legendary'
    ]
];

$insertQuery = "INSERT INTO shop_items (item_id, name, description, price, quantity, image_url, category_id, rarity) VALUES (?, ?, ?, ?, ?, '/images/items/default.png', 1, ?)";
$stmt = $conn->prepare($insertQuery);

foreach ($newItems as $item) {
    $stmt->bind_param("issiiis", 
        $item['item_id'], 
        $item['name'], 
        $item['description'], 
        $item['price'], 
        $item['quantity'], 
        $item['quantity'],  // Bind quantity twice (for the two ? placeholders)
        $item['rarity']
    );
    
    if ($stmt->execute()) {
        echo "✓ Added: {$item['name']} - {$item['price']} DP\n";
    } else {
        echo "✗ Error adding {$item['name']}: " . $stmt->error . "\n";
    }
}

echo "\n✓ Shop items added successfully!\n";
echo "Visit the shop to see the new items.\n";

$conn->close();
?>

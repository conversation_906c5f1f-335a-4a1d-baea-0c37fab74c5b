<?php
session_start();
require_once "db.php";

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['order_id']) || !isset($input['amount'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid input']);
    exit;
}

// Create database connection
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed']);
    exit;
}

$accountName = $_SESSION["username"];

// Get account ID
$accountQuery = "SELECT id FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $accountName);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    http_response_code(404);
    echo json_encode(['success' => false, 'message' => 'Account not found']);
    exit;
}

$userId = $account['id'];
$orderId = $input['order_id'];
$amount = floatval($input['amount']);
$payerName = $input['payer_name'] ?? '';
$payerEmail = $input['payer_email'] ?? '';

// Validate amount
if ($amount < 1 || $amount > 500) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid donation amount']);
    exit;
}

// Check if order already exists
$checkQuery = "SELECT id FROM donations WHERE paypal_order_id = ?";
$stmt = $conn->prepare($checkQuery);
$stmt->bind_param("s", $orderId);
$stmt->execute();
$checkResult = $stmt->get_result();

if ($checkResult->num_rows > 0) {
    echo json_encode(['success' => false, 'message' => 'Donation already processed']);
    exit;
}

// Insert donation record
$insertQuery = "
    INSERT INTO donations (user_id, amount, paypal_order_id, status, created_at)
    VALUES (?, ?, ?, 'completed', NOW())
";

$stmt = $conn->prepare($insertQuery);
$stmt->bind_param("ids", $userId, $amount, $orderId);

if ($stmt->execute()) {
    $donationId = $conn->insert_id;
    
    // Update membership level based on donation amount
    $newMembership = 0;
    if ($amount >= 50) $newMembership = 4; // Platinum
    elseif ($amount >= 25) $newMembership = 3; // Gold
    elseif ($amount >= 10) $newMembership = 2; // Silver
    elseif ($amount >= 5) $newMembership = 1; // Bronze

    // Update account membership if higher than current
    $updateMembershipQuery = "
        UPDATE account_data
        SET membership = GREATEST(membership, ?)
        WHERE id = ?
    ";
    $stmt = $conn->prepare($updateMembershipQuery);
    $stmt->bind_param("ii", $newMembership, $userId);
    $stmt->execute();

    // Add donation points (1 euro = 1 point)
    $donationPoints = intval($amount);
    $priceId = 1; // Donation Points currency

    // Check if account balance exists
    $balanceCheckQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = ?";
    $stmt = $conn->prepare($balanceCheckQuery);
    $stmt->bind_param("ii", $userId, $priceId);
    $stmt->execute();
    $balanceResult = $stmt->get_result();

    if ($balanceResult->num_rows > 0) {
        // Update existing balance
        $updateBalanceQuery = "UPDATE account_balance SET value = value + ? WHERE account_id = ? AND price_id = ?";
        $stmt = $conn->prepare($updateBalanceQuery);
        $stmt->bind_param("iii", $donationPoints, $userId, $priceId);
        $stmt->execute();
    } else {
        // Insert new balance record
        $insertBalanceQuery = "INSERT INTO account_balance (account_id, account_name, price_id, value) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($insertBalanceQuery);
        $stmt->bind_param("isii", $userId, $accountName, $priceId, $donationPoints);
        $stmt->execute();
    }

    // Log the transaction
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, ?, ?, 'PayPal Donation', 'IN')";
    $stmt = $conn->prepare($historyQuery);
    $stmt->bind_param("ssiii", $orderId, $userId, $accountName, $priceId, $donationPoints);
    $stmt->execute();
    
    // Get player characters for rewards
    $playersQuery = "SELECT id, name FROM players WHERE account_id = ? LIMIT 1";
    $stmt = $conn->prepare($playersQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $playersResult = $stmt->get_result();
    
    if ($playersResult->num_rows > 0) {
        $player = $playersResult->fetch_assoc();
        $playerId = $player['id'];
        
        // Determine rewards based on donation amount
        $rewards = [];
        if ($amount >= 5) {
            $rewards[] = ['item_id' => *********, 'count' => 10]; // Kinah (example)
        }
        if ($amount >= 10) {
            $rewards[] = ['item_id' => *********, 'count' => 5]; // Premium items
        }
        if ($amount >= 25) {
            $rewards[] = ['item_id' => *********, 'count' => 3]; // Rare items
        }
        if ($amount >= 50) {
            $rewards[] = ['item_id' => *********, 'count' => 1]; // Legendary item
        }
        
        // Insert web rewards
        foreach ($rewards as $reward) {
            $rewardQuery = "
                INSERT INTO player_web_rewards (player_id, item_id, item_count, added, order_id)
                VALUES (?, ?, ?, NOW(), ?)
            ";
            $stmt = $conn->prepare($rewardQuery);
            $orderRef = $orderId . '_' . $reward['item_id'];
            $stmt->bind_param("iiis", $playerId, $reward['item_id'], $reward['count'], $orderRef);
            $stmt->execute();
        }
    }
    
    // Log the transaction
    error_log("Donation processed: User ID $userId, Amount $amount, Order ID $orderId");
    
    echo json_encode([
        'success' => true, 
        'message' => 'Donation processed successfully',
        'donation_id' => $donationId,
        'membership_level' => $newMembership
    ]);
} else {
    error_log("Failed to insert donation: " . $conn->error);
    echo json_encode(['success' => false, 'message' => 'Failed to process donation']);
}

$conn->close();
?>

<?php
session_start();
require_once "db.php";

// Simple test page to check purchase functionality
if (!isset($_SESSION["username"])) {
    echo "Please login first";
    exit;
}

$username = $_SESSION["username"];

// Get account info
$accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
$stmt = $conn->prepare($accountQuery);
$stmt->bind_param("s", $username);
$stmt->execute();
$accountResult = $stmt->get_result();
$account = $accountResult->fetch_assoc();

if (!$account) {
    echo "Account not found";
    exit;
}

// Get current balance
$balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
$stmt = $conn->prepare($balanceQuery);
$stmt->bind_param("i", $account['id']);
$stmt->execute();
$balanceResult = $stmt->get_result();
$balance = $balanceResult->fetch_assoc();
$userPoints = $balance['value'] ?? 0;

echo "<h2>Purchase Test Page</h2>";
echo "<p>Logged in as: <strong>$username</strong> (ID: {$account['id']})</p>";
echo "<p>Current Balance: <strong>$userPoints DP</strong></p>";

// Get shop items
$itemsQuery = "SELECT id, item_id, name, description, price, quantity FROM shop_items ORDER BY quantity ASC";
$itemsResult = $conn->query($itemsQuery);

echo "<h3>Available Items:</h3>";
if ($itemsResult->num_rows > 0) {
    while ($item = $itemsResult->fetch_assoc()) {
        $canBuy = $userPoints >= $item['price'];
        $buttonClass = $canBuy ? 'style="background: green; color: white; padding: 10px; border: none; cursor: pointer;"' : 'style="background: gray; color: white; padding: 10px; border: none;" disabled';
        
        echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0;'>";
        echo "<h4>{$item['name']}</h4>";
        echo "<p>{$item['description']}</p>";
        echo "<p><strong>Quantity:</strong> {$item['quantity']}</p>";
        echo "<p><strong>Price:</strong> {$item['price']} DP</p>";
        
        if ($canBuy) {
            echo "<button $buttonClass onclick=\"testPurchase({$item['id']}, '{$item['name']}', {$item['price']}, {$item['quantity']})\">Purchase</button>";
        } else {
            echo "<button $buttonClass>Insufficient Points</button>";
        }
        echo "</div>";
    }
} else {
    echo "<p>No items available</p>";
}

$conn->close();
?>

<script>
function testPurchase(itemId, itemName, price, quantity) {
    if (confirm('Purchase "' + itemName + '" for ' + price + ' DP?')) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'purchase_item.php', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                console.log('Response status:', xhr.status);
                console.log('Response text:', xhr.responseText);
                
                if (xhr.status === 200) {
                    try {
                        var data = JSON.parse(xhr.responseText);
                        if (data.success) {
                            alert('SUCCESS: ' + data.message);
                            location.reload();
                        } else {
                            alert('FAILED: ' + data.message);
                        }
                    } catch (e) {
                        alert('JSON Error: ' + e.message + '\nResponse: ' + xhr.responseText);
                    }
                } else {
                    alert('HTTP Error: ' + xhr.status + '\nResponse: ' + xhr.responseText);
                }
            }
        };
        
        var requestData = {
            item_id: itemId,
            price: price,
            quantity: quantity
        };
        
        console.log('Sending request:', requestData);
        xhr.send(JSON.stringify(requestData));
    }
}
</script>

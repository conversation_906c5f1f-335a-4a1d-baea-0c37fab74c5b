<?php
// Update shop to only sell item 164000073 with quantity selection
require_once "db.php";

echo "Updating shop items...\n";

// Add quantity column if it doesn't exist
$addColumnQuery = "ALTER TABLE shop_items ADD COLUMN IF NOT EXISTS quantity INT(11) DEFAULT 1";
if ($conn->query($addColumnQuery) === TRUE) {
    echo "✓ Added quantity column to shop_items table\n";
} else {
    echo "✗ Error adding quantity column: " . $conn->error . "\n";
}

// Clear existing shop items
$clearQuery = "DELETE FROM shop_items";
if ($conn->query($clearQuery) === TRUE) {
    echo "✓ Cleared existing shop items\n";
} else {
    echo "✗ Error clearing items: " . $conn->error . "\n";
}

// Add only the item 164000073 with different quantity options
$items = [
    ['item_id' => 164000073, 'name' => 'Test Reward Item (x1)', 'description' => 'A special test item for daily rewards - Single item', 'price' => 10, 'quantity' => 1],
    ['item_id' => 164000073, 'name' => 'Test Reward Item (x5)', 'description' => 'A special test item for daily rewards - Pack of 5', 'price' => 45, 'quantity' => 5],
    ['item_id' => 164000073, 'name' => 'Test Reward Item (x10)', 'description' => 'A special test item for daily rewards - Pack of 10', 'price' => 85, 'quantity' => 10],
    ['item_id' => 164000073, 'name' => 'Test Reward Item (x25)', 'description' => 'A special test item for daily rewards - Pack of 25', 'price' => 200, 'quantity' => 25],
    ['item_id' => 164000073, 'name' => 'Test Reward Item (x50)', 'description' => 'A special test item for daily rewards - Pack of 50', 'price' => 375, 'quantity' => 50],
    ['item_id' => 164000073, 'name' => 'Test Reward Item (x100)', 'description' => 'A special test item for daily rewards - Pack of 100', 'price' => 700, 'quantity' => 100]
];

$insertQuery = "INSERT INTO shop_items (item_id, name, description, price, quantity, image_url, category_id, rarity) VALUES (?, ?, ?, ?, ?, '/images/items/164000073.png', 1, 'Common')";
$stmt = $conn->prepare($insertQuery);

foreach ($items as $item) {
    $stmt->bind_param("issii", $item['item_id'], $item['name'], $item['description'], $item['price'], $item['quantity']);
    if ($stmt->execute()) {
        echo "✓ Added: {$item['name']} - {$item['price']} DP\n";
    } else {
        echo "✗ Error adding {$item['name']}: " . $stmt->error . "\n";
    }
}

echo "\n✓ Shop updated successfully!\n";
echo "Now selling only item 164000073 with quantity options.\n";

$conn->close();
?>

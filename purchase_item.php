<?php
session_start();
require_once "db.php";

// Enable error logging
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION["username"])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not logged in']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['item_id'], $input['price'], $input['quantity'], $input['recipient'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

$itemId = intval($input['item_id']);
$expectedPrice = intval($input['price']);
$expectedQuantity = intval($input['quantity']);
$recipient = $input['recipient'];

try {
    // Get account info
    $accountQuery = "SELECT id, name FROM account_data WHERE name = ?";
    $stmt = $conn->prepare($accountQuery);
    $stmt->bind_param("s", $_SESSION["username"]);
    $stmt->execute();
    $accountResult = $stmt->get_result();
    $account = $accountResult->fetch_assoc();

    if (!$account) {
        throw new Exception("Account not found");
    }

    $userId = $account['id'];
    $accountName = $account['name'];

    // Get item details
    $itemQuery = "SELECT id, item_id, name, price, quantity FROM shop_items WHERE id = ?";
    $stmt = $conn->prepare($itemQuery);
    $stmt->bind_param("i", $itemId);
    $stmt->execute();
    $itemResult = $stmt->get_result();
    $item = $itemResult->fetch_assoc();

    if (!$item) {
        throw new Exception("Item not found");
    }

    // Verify price and quantity
    if ($item['price'] != $expectedPrice) {
        throw new Exception("Price mismatch");
    }

    if ($item['quantity'] != $expectedQuantity) {
        throw new Exception("Quantity mismatch");
    }

    // Check user balance
    $balanceQuery = "SELECT value FROM account_balance WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($balanceQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $balanceResult = $stmt->get_result();
    $balance = $balanceResult->fetch_assoc();
    $userPoints = $balance['value'] ?? 0;

    if ($userPoints < $item['price']) {
        throw new Exception("Insufficient points");
    }

    // Handle recipient (self or gift)
    $isGift = ($recipient['type'] === 'gift');

    if ($isGift) {
        // Gift to another player - find character by name
        $giftCharacterName = $recipient['character_name'];
        $playerQuery = "SELECT id, name, account_id FROM players WHERE name = ?";
        $stmt = $conn->prepare($playerQuery);
        $stmt->bind_param("s", $giftCharacterName);
        $stmt->execute();
        $playerResult = $stmt->get_result();
        $player = $playerResult->fetch_assoc();

        if (!$player) {
            throw new Exception("Recipient character '$giftCharacterName' not found");
        }

        // Prevent self-gifting
        if ($player['account_id'] == $userId) {
            throw new Exception("You cannot gift items to your own characters");
        }

        $playerId = $player['id'];
        $playerName = $player['name'];
        $gifterName = $accountName;
    } else {
        // Send to own character
        $characterId = $recipient['character_id'];
        $playerQuery = "SELECT id, name FROM players WHERE id = ? AND account_id = ?";
        $stmt = $conn->prepare($playerQuery);
        $stmt->bind_param("ii", $characterId, $userId);
        $stmt->execute();
        $playerResult = $stmt->get_result();
        $player = $playerResult->fetch_assoc();

        if (!$player) {
            throw new Exception("Character not found or does not belong to your account");
        }

        $playerId = $player['id'];
        $playerName = $player['name'];
        $gifterName = '';
    }

    // Start transaction
    $conn->begin_transaction();

    // FIXED: Use shop_purchases table instead of direct inventory insertion
    // This prevents ID conflicts and uses the game server's built-in system
    $shopPurchaseQuery = "INSERT INTO shop_purchases (char_id, item_id, quantity, gift, gifter, added) VALUES (?, ?, ?, ?, ?, 0)";
    $stmt = $conn->prepare($shopPurchaseQuery);
    
    $gameItemId = $item['item_id'];
    $itemQuantity = $item['quantity'];
    $isGiftFlag = $isGift ? 1 : 0;
    
    $stmt->bind_param("iiiis", $playerId, $gameItemId, $itemQuantity, $isGiftFlag, $gifterName);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to create shop purchase: " . $stmt->error);
    }

    $shopPurchaseId = $conn->insert_id;

    // Deduct points from balance
    $deductQuery = "UPDATE account_balance SET value = value - ? WHERE account_id = ? AND price_id = 1";
    $stmt = $conn->prepare($deductQuery);
    $itemPrice = $item['price'];
    $stmt->bind_param("ii", $itemPrice, $userId);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to deduct points: " . $stmt->error);
    }

    // Add transaction history
    $historyQuery = "INSERT INTO balance_history (date, reference_id, account_id, account_name, price_id, amount, description, type) VALUES (NOW(), ?, ?, ?, 1, ?, ?, 'OUT')";
    $orderRef = 'SHOP_' . $userId . '_' . time();
    $description = 'Purchased: ' . $item['name'] . ' (x' . $item['quantity'] . ')';
    $stmt = $conn->prepare($historyQuery);
    $stmt->bind_param("ssiss", $orderRef, $userId, $accountName, $itemPrice, $description);
    
    if (!$stmt->execute()) {
        throw new Exception("Failed to record transaction: " . $stmt->error);
    }

    // Commit transaction
    $conn->commit();

    // Success response
    if ($isGift) {
        $successMessage = "Gift sent successfully! Item will be delivered to '$playerName' when they log in. They will receive it automatically via the game server's shop system.";
    } else {
        $successMessage = "Purchase successful! Item will be delivered to character '$playerName' when you log in. The game server will automatically add it to your inventory.";
    }

    echo json_encode([
        'success' => true,
        'message' => $successMessage,
        'remaining_points' => $userPoints - $item['price'],
        'character_name' => $playerName,
        'is_gift' => $isGift,
        'shop_purchase_id' => $shopPurchaseId,
        'delivery_method' => 'Game server will automatically deliver items on login'
    ]);

} catch (Exception $e) {
    // Rollback transaction on error
    if (isset($conn)) {
        $conn->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'Purchase failed: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
